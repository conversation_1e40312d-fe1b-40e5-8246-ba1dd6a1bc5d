#!/usr/bin/env node

/**
 * 配置更新验证脚本
 * 验证MongoDB和MinIO配置是否正确更新
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { Client: MinioClient } = require('minio');

// 新的配置参数
const EXPECTED_CONFIG = {
  mongodb: {
    host: '************',
    port: '27017',
    username: 'lcs',
    password: 'Sa2482047260',
    database: 'products',
    authSource: 'admin'
  },
  minio: {
    endpoint: '************',
    port: 9000,
    accessKey: 'lcsm',
    secretKey: 'Sa2482047260',
    webConsole: 'http://************:9001'
  }
};

async function verifyConfiguration() {
  console.log('🔍 开始验证配置更新...\n');

  // 1. 验证环境变量
  console.log('1. 验证环境变量配置:');
  
  const mongoUri = process.env.MONGODB_URI;
  const minioEndpoint = process.env.MINIO_ENDPOINT;
  const minioPort = process.env.MINIO_PORT;
  const minioAccessKey = process.env.MINIO_ACCESS_KEY;
  const minioSecretKey = process.env.MINIO_SECRET_KEY;

  console.log(`   MongoDB URI: ${mongoUri}`);
  console.log(`   MinIO Endpoint: ${minioEndpoint}`);
  console.log(`   MinIO Port: ${minioPort}`);
  console.log(`   MinIO Access Key: ${minioAccessKey}`);
  console.log(`   MinIO Secret Key: ${minioSecretKey ? '***' + minioSecretKey.slice(-4) : 'Not set'}`);

  // 验证MongoDB URI格式
  if (mongoUri && mongoUri.includes(EXPECTED_CONFIG.mongodb.host)) {
    console.log('   ✅ MongoDB配置包含正确的IP地址');
  } else {
    console.log('   ❌ MongoDB配置IP地址不正确');
    return false;
  }

  // 验证MinIO配置
  if (minioEndpoint === EXPECTED_CONFIG.minio.endpoint) {
    console.log('   ✅ MinIO端点配置正确');
  } else {
    console.log('   ❌ MinIO端点配置不正确');
    return false;
  }

  if (minioAccessKey === EXPECTED_CONFIG.minio.accessKey) {
    console.log('   ✅ MinIO访问密钥配置正确');
  } else {
    console.log('   ❌ MinIO访问密钥配置不正确');
    return false;
  }

  if (minioSecretKey === EXPECTED_CONFIG.minio.secretKey) {
    console.log('   ✅ MinIO密钥配置正确');
  } else {
    console.log('   ❌ MinIO密钥配置不正确');
    return false;
  }

  console.log('\n2. 测试MongoDB连接:');
  try {
    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 5000,
    });
    console.log('   ✅ MongoDB连接成功');
    await mongoose.disconnect();
  } catch (error) {
    console.log('   ❌ MongoDB连接失败:', error.message);
    return false;
  }

  console.log('\n3. 测试MinIO连接:');
  try {
    const minioClient = new MinioClient({
      endPoint: minioEndpoint,
      port: parseInt(minioPort),
      useSSL: false,
      accessKey: minioAccessKey,
      secretKey: minioSecretKey
    });

    // 测试连接
    const bucketExists = await minioClient.bucketExists('product-images');
    console.log('   ✅ MinIO连接成功');
    console.log(`   ✅ 存储桶 'product-images' ${bucketExists ? '存在' : '不存在'}`);
  } catch (error) {
    console.log('   ❌ MinIO连接失败:', error.message);
    return false;
  }

  console.log('\n4. 验证配置文件:');
  
  // 检查TypeScript配置文件
  try {
    const { IMAGE_CONFIG } = require('./dist/config/imageConfig');
    
    if (IMAGE_CONFIG.MINIO.ENDPOINT === EXPECTED_CONFIG.minio.endpoint) {
      console.log('   ✅ imageConfig.ts中的MinIO端点配置正确');
    } else {
      console.log('   ❌ imageConfig.ts中的MinIO端点配置不正确');
      console.log(`      期望: ${EXPECTED_CONFIG.minio.endpoint}, 实际: ${IMAGE_CONFIG.MINIO.ENDPOINT}`);
      return false;
    }

    if (IMAGE_CONFIG.MINIO.ACCESS_KEY === EXPECTED_CONFIG.minio.accessKey) {
      console.log('   ✅ imageConfig.ts中的MinIO访问密钥配置正确');
    } else {
      console.log('   ❌ imageConfig.ts中的MinIO访问密钥配置不正确');
      return false;
    }

    if (IMAGE_CONFIG.MINIO.SECRET_KEY === EXPECTED_CONFIG.minio.secretKey) {
      console.log('   ✅ imageConfig.ts中的MinIO密钥配置正确');
    } else {
      console.log('   ❌ imageConfig.ts中的MinIO密钥配置不正确');
      return false;
    }

  } catch (error) {
    console.log('   ⚠️ 无法验证TypeScript配置文件，请先编译项目');
    console.log('   运行: npm run build');
  }

  console.log('\n✅ 所有配置验证通过！');
  console.log('\n📋 配置摘要:');
  console.log(`   MongoDB: ${EXPECTED_CONFIG.mongodb.host}:${EXPECTED_CONFIG.mongodb.port}`);
  console.log(`   MinIO API: http://${EXPECTED_CONFIG.minio.endpoint}:${EXPECTED_CONFIG.minio.port}`);
  console.log(`   MinIO Web控制台: ${EXPECTED_CONFIG.minio.webConsole}`);
  
  return true;
}

// 运行验证
verifyConfiguration()
  .then(success => {
    if (success) {
      console.log('\n🎉 配置更新验证完成！');
      process.exit(0);
    } else {
      console.log('\n❌ 配置验证失败，请检查上述错误信息');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('\n💥 验证过程中发生错误:', error);
    process.exit(1);
  });
