#!/usr/bin/env node

/**
 * 测试新的飞书配置是否正常工作
 */

require('dotenv').config();
const axios = require('axios');

async function testFeishuConfig() {
  console.log('🔧 测试飞书配置...');
  console.log('App ID:', process.env.FEISHU_APP_ID);
  console.log('App Token:', process.env.FEISHU_APP_TOKEN);
  console.log('Table ID:', process.env.FEISHU_TABLE_ID);
  console.log('');

  try {
    // 1. 测试获取访问令牌
    console.log('🔑 测试获取访问令牌...');
    const tokenResponse = await axios.post(
      'https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal',
      {
        app_id: process.env.FEISHU_APP_ID,
        app_secret: process.env.FEISHU_APP_SECRET
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (tokenResponse.data.code !== 0) {
      throw new Error(`获取令牌失败: ${tokenResponse.data.msg}`);
    }

    const accessToken = tokenResponse.data.tenant_access_token;
    console.log('✅ 访问令牌获取成功');

    // 2. 测试获取记录（只获取前5条）
    console.log('📝 测试获取记录...');
    const recordsResponse = await axios.get(
      `https://open.feishu.cn/open-apis/bitable/v1/apps/${process.env.FEISHU_APP_TOKEN}/tables/${process.env.FEISHU_TABLE_ID}/records`,
      {
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        params: {
          page_size: 5
        }
      }
    );

    if (recordsResponse.data.code !== 0) {
      throw new Error(`获取记录失败: ${recordsResponse.data.msg}`);
    }

    console.log('✅ 记录获取成功');
    console.log('记录数量:', recordsResponse.data.data.items.length);
    console.log('总记录数:', recordsResponse.data.data.total);

    console.log('\n🎉 飞书配置测试完成，所有功能正常！');

  } catch (error) {
    console.error('❌ 飞书配置测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', JSON.stringify(error.response.data, null, 2));
    }
    process.exit(1);
  }
}

// 运行测试
testFeishuConfig();
