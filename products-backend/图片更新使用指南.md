# 图片更新机制使用指南

## 概述

本指南介绍如何使用项目中的图片更新机制，从飞书多维表格同步产品图片到本地MinIO存储系统。

## 快速开始

### 1. 环境准备

确保以下环境变量已正确配置：

```bash
# 飞书API配置
FEISHU_APP_ID=your_app_id
FEISHU_APP_SECRET=your_app_secret
FEISHU_APP_TOKEN=your_app_token
FEISHU_TABLE_ID=your_table_id

# MinIO配置
MINIO_ENDPOINT=192.229.85.5
MINIO_PORT=9000
MINIO_ACCESS_KEY=lcsm
MINIO_SECRET_KEY=Sa2482047260
MINIO_BUCKET=product-images

# 数据库配置
MONGODB_URI=your_mongodb_uri
```

### 2. 基本使用

#### 测试飞书连接
```bash
node test-feishu-download.js
```

#### 测试完整图片下载流程
```bash
node test-image-download.js
```

#### 更新产品图片引用
```bash
node update-product-images.js
```

#### 从飞书同步最新图片
```bash
# 更新所有产品
node update-images-from-feishu.js

# 更新指定产品
node update-images-from-feishu.js recn7j9p5p recyvMPGSQ
```

## 详细功能说明

### 1. 图片下载和存储

#### 支持的图片类型
- `front`: 正面图片
- `back`: 背面图片  
- `label`: 标签图片
- `package`: 外包装图片
- `gift`: 赠品图片

#### 存储路径规范
```
product-images/
├── products/                    # 原始图片
│   └── {productId}_{imageType}_{timestamp}.{ext}
├── thumbnails/                  # 缩略图
│   ├── small/                  # 150x150
│   ├── medium/                 # 300x300
│   └── large/                  # 600x600
└── temp/                       # 临时文件
```

#### 自动生成缩略图
系统会自动为每张图片生成三种尺寸的WebP格式缩略图：
- 小图：150x150，质量80%
- 中图：300x300，质量85%
- 大图：600x600，质量90%

### 2. 数据库记录

#### Product表图片字段
```typescript
images: {
  front?: string;    // MinIO完整URL
  back?: string;
  label?: string;
  package?: string;
  gift?: string;
}
```

#### Image表详细记录
每张图片在Image表中都有完整的元数据记录，包括：
- 文件信息（大小、格式、尺寸）
- 存储信息（桶名、对象名、URL）
- 飞书信息（文件令牌、下载时间）
- 处理状态和缩略图信息

### 3. 错误处理和修复

#### 图片完整性检查
```bash
# 检查图片文件是否存在
node scripts/check-image-integrity.js

# 修复缺失的图片
node scripts/repair-missing-images.js
```

#### 常见问题解决

**问题1：图片下载失败**
- 检查飞书API权限和令牌有效性
- 确认MinIO服务正常运行
- 查看错误日志获取详细信息

**问题2：图片显示异常**
- 运行 `update-product-images.js` 更新图片引用
- 检查MinIO存储桶权限设置
- 验证图片URL格式是否正确

**问题3：缩略图生成失败**
- 确认Sharp图片处理库正常安装
- 检查原始图片格式是否支持
- 查看服务器内存和磁盘空间

## 高级用法

### 1. 批量图片同步

```javascript
const { ImageService } = require('./dist/services/imageService');
const imageService = new ImageService();

// 批量下载图片
const imageJobs = [
  {
    productId: 'recn7j9p5p',
    imageType: 'front',
    fileTokens: ['Vnjsb2KTsouUWBx6oiVcthhinL0']
  },
  // ... 更多图片任务
];

const result = await imageService.batchDownloadFromFeishu(imageJobs);
console.log(`成功: ${result.successful.length}, 失败: ${result.failed.length}`);
```

### 2. 图片修复和重新下载

```javascript
const { ImageService } = require('./dist/services/imageService');
const imageService = new ImageService();

// 修复特定产品的图片
const repairResult = await imageService.repairProductImages('recn7j9p5p');
console.log('修复结果:', repairResult);
```

### 3. 自定义图片处理

```javascript
const { ImageService } = require('./dist/services/imageService');
const imageService = new ImageService();

// 上传自定义图片
const buffer = fs.readFileSync('custom-image.jpg');
const imageRecord = await imageService.uploadImage(
  buffer,
  'custom-image.jpg',
  'productId123',
  'front'
);
```

## 监控和日志

### 1. 日志查看
```bash
# 查看实时日志
tail -f logs/app.log

# 查看图片相关日志
grep "图片" logs/app.log

# 查看错误日志
grep "ERROR" logs/app.log
```

### 2. 性能监控
- 图片下载速度和成功率
- MinIO存储使用情况
- 数据库查询性能
- 缩略图生成时间

## 最佳实践

### 1. 定期维护
- 每周运行一次图片完整性检查
- 定期清理临时文件和孤立图片
- 监控存储空间使用情况

### 2. 性能优化
- 使用并发控制避免过多同时下载
- 合理设置图片质量和尺寸
- 启用CDN加速图片访问

### 3. 安全考虑
- 定期更新飞书API令牌
- 设置MinIO访问权限
- 备份重要图片数据

## 故障排除

### 1. 常见错误代码
- `FEISHU_TOKEN_EXPIRED`: 飞书令牌过期，需要刷新
- `MINIO_CONNECTION_FAILED`: MinIO连接失败，检查网络和配置
- `IMAGE_PROCESSING_ERROR`: 图片处理失败，检查文件格式

### 2. 调试技巧
- 启用详细日志模式
- 使用测试脚本验证各个环节
- 检查网络连接和防火墙设置

### 3. 紧急恢复
- 从备份恢复数据库
- 重新下载缺失的图片
- 重建图片索引和引用

## 联系支持

如果遇到问题，请：
1. 查看日志文件获取详细错误信息
2. 运行相关测试脚本验证功能
3. 提供完整的错误堆栈和环境信息
