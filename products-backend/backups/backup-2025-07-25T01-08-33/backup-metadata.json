{"timestamp": "2025-07-25T01:08:42.015Z", "database": "products", "backupPath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-25T01-08-33", "collections": [{"collection": "categories", "documentCount": 0, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-25T01-08-33/categories.json", "fileSize": 2}, {"collection": "newproducts", "documentCount": 0, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-25T01-08-33/newproducts.json", "fileSize": 2}, {"collection": "synclogs", "documentCount": 18, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-25T01-08-33/synclogs.json", "fileSize": 16741}, {"collection": "images", "documentCount": 2595, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-25T01-08-33/images.json", "fileSize": 5169834}, {"collection": "products", "documentCount": 1199, "filePath": "/Users/<USER>/Desktop/projects/products-b-test/products-backend/backups/backup-2025-07-25T01-08-33/products.json", "fileSize": 2064665}], "databaseStats": {"collectionCount": 5, "documentCount": 3812, "backupSize": 7251244}, "mongodbUri": "mongodb://lcs:****@192.229.85.5:27017/products?authSource=admin", "version": "1.0", "type": "pre-schema-migration", "method": "mongoose-json-export"}